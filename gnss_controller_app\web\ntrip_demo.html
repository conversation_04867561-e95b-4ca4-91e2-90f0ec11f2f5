<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>NTRIP Configuration</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background-color: #f5f5f5;
            padding: 20px;
            line-height: 1.6;
        }

        .container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            border-radius: 12px;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
            overflow: hidden;
        }

        .header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 30px;
            text-align: center;
        }

        .header h1 {
            font-size: 2.5em;
            margin-bottom: 10px;
            font-weight: 300;
        }

        .header p {
            opacity: 0.9;
            font-size: 1.1em;
        }

        .content {
            padding: 40px;
        }

        .section {
            margin-bottom: 40px;
            border: 2px solid #e1e5e9;
            border-radius: 12px;
            overflow: hidden;
        }

        .section-header {
            background-color: #f8f9fa;
            padding: 20px;
            border-bottom: 1px solid #e1e5e9;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .section-title {
            font-size: 1.3em;
            font-weight: 600;
            color: #2c3e50;
        }

        .status-info {
            font-size: 0.9em;
            color: #6c757d;
        }

        .toggle-switch {
            position: relative;
            width: 60px;
            height: 30px;
            background-color: #2196F3;
            border-radius: 15px;
            cursor: pointer;
            transition: background-color 0.3s;
        }

        .toggle-switch::after {
            content: '';
            position: absolute;
            top: 3px;
            right: 3px;
            width: 24px;
            height: 24px;
            background-color: white;
            border-radius: 50%;
            transition: transform 0.3s;
        }

        .section-content {
            padding: 30px;
        }

        .form-row {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 20px;
            margin-bottom: 25px;
        }

        .form-row.single {
            grid-template-columns: 1fr;
        }

        .form-group {
            display: flex;
            flex-direction: column;
        }

        .form-group label {
            font-weight: 500;
            color: #495057;
            margin-bottom: 8px;
            font-size: 0.95em;
        }

        .form-group input {
            padding: 12px 16px;
            border: 2px solid #e1e5e9;
            border-radius: 8px;
            font-size: 1em;
            transition: border-color 0.3s, box-shadow 0.3s;
            background-color: #fff;
        }

        .form-group input:focus {
            outline: none;
            border-color: #2196F3;
            box-shadow: 0 0 0 3px rgba(33, 150, 243, 0.1);
        }

        .form-group input[type="password"] {
            font-family: monospace;
            letter-spacing: 2px;
        }

        .port-group {
            display: flex;
            align-items: flex-end;
            gap: 10px;
        }

        .port-group input {
            flex: 1;
        }

        .port-separator {
            padding: 12px 8px;
            font-weight: bold;
            color: #6c757d;
        }

        .caster-grid {
            display: grid;
            grid-template-columns: 1fr 1fr 1fr 1fr;
            gap: 20px;
            margin-bottom: 25px;
        }

        .demo-note {
            background-color: #e3f2fd;
            border-left: 4px solid #2196F3;
            padding: 20px;
            margin-top: 30px;
            border-radius: 0 8px 8px 0;
        }

        .demo-note h3 {
            color: #1976d2;
            margin-bottom: 10px;
        }

        .demo-note p {
            color: #424242;
            margin-bottom: 8px;
        }

        @media (max-width: 768px) {
            .form-row {
                grid-template-columns: 1fr;
            }
            
            .caster-grid {
                grid-template-columns: 1fr 1fr;
            }
            
            .content {
                padding: 20px;
            }
        }

        @media (max-width: 480px) {
            .caster-grid {
                grid-template-columns: 1fr;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>NTRIP Configuration</h1>
            <p>Real-Time Kinematic Positioning Service</p>
        </div>

        <div class="content">
            <!-- NTRIP Server Section -->
            <div class="section">
                <div class="section-header">
                    <div>
                        <div class="section-title">NTRIP server</div>
                        <div class="status-info">0B in (0B/s) / 0B out (0B/s)</div>
                    </div>
                    <div class="toggle-switch"></div>
                </div>
                <div class="section-content">
                    <div class="form-row">
                        <div class="form-group">
                            <label for="server-host">Host and port</label>
                            <div class="port-group">
                                <input type="text" id="server-host" value="********" placeholder="Host">
                                <span class="port-separator">:</span>
                                <input type="text" id="server-port" value="3101" placeholder="Port" style="width: 80px;">
                            </div>
                        </div>
                        <div class="form-group">
                            <label for="server-mountpoint">Mountpoint</label>
                            <input type="text" id="server-mountpoint" value="H2K_SHOP" placeholder="Mountpoint">
                        </div>
                    </div>
                    <div class="form-row">
                        <div class="form-group">
                            <label for="server-username">Username</label>
                            <input type="text" id="server-username" value="admin" placeholder="Username">
                        </div>
                        <div class="form-group">
                            <label for="server-password">Password</label>
                            <input type="password" id="server-password" value="password" placeholder="Password">
                        </div>
                    </div>
                </div>
            </div>

            <!-- NTRIP Caster Section -->
            <div class="section">
                <div class="section-header">
                    <div>
                        <div class="section-title">NTRIP caster</div>
                        <div class="status-info">0B in (0B/s) / 0B out (0B/s)</div>
                    </div>
                    <div class="toggle-switch"></div>
                </div>
                <div class="section-content">
                    <div class="caster-grid">
                        <div class="form-group">
                            <label for="caster-port">Port</label>
                            <input type="text" id="caster-port" value="4101" placeholder="Port">
                        </div>
                        <div class="form-group">
                            <label for="caster-mountpoint">Mountpoint</label>
                            <input type="text" id="caster-mountpoint" value="H2K_SHOP" placeholder="Mountpoint">
                        </div>
                        <div class="form-group">
                            <label for="caster-username">Username</label>
                            <input type="text" id="caster-username" value="admin" placeholder="Username">
                        </div>
                        <div class="form-group">
                            <label for="caster-password">Password</label>
                            <input type="password" id="caster-password" value="password" placeholder="Password">
                        </div>
                    </div>
                </div>
            </div>

            <!-- Demo Information -->
            <div class="demo-note">
                <h3>🚀 Demo NTRIP Interface</h3>
                <p><strong>Server:</strong> Connects to external NTRIP correction services</p>
                <p><strong>Caster:</strong> Broadcasts correction data to NTRIP clients</p>
                <p><strong>Note:</strong> This is a demonstration interface. Toggle switches and form fields are for display purposes.</p>
            </div>
        </div>
    </div>

    <script>
        // Simple toggle animation
        document.querySelectorAll('.toggle-switch').forEach(toggle => {
            toggle.addEventListener('click', function() {
                this.style.backgroundColor = this.style.backgroundColor === 'rgb(158, 158, 158)' ? '#2196F3' : '#9e9e9e';
            });
        });

        // Form interaction feedback
        document.querySelectorAll('input').forEach(input => {
            input.addEventListener('focus', function() {
                this.parentElement.style.transform = 'translateY(-2px)';
                this.parentElement.style.transition = 'transform 0.2s ease';
            });
            
            input.addEventListener('blur', function() {
                this.parentElement.style.transform = 'translateY(0)';
            });
        });
    </script>
</body>
</html>
