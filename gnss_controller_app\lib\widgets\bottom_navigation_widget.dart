import 'package:flutter/material.dart';
import '../screens/main_screen.dart';
import '../screens/terminal_screen.dart';

class BottomNavigationWidget extends StatelessWidget {
  final int currentIndex;

  const BottomNavigationWidget({
    super.key,
    required this.currentIndex,
  });

  @override
  Widget build(BuildContext context) {
    // Ensure currentIndex is within valid range (0-2)
    int validIndex = currentIndex;
    if (validIndex < 0 || validIndex >= 3) {
      validIndex = 0; // Default to first tab
    }

    return BottomNavigationBar(
      type: BottomNavigationBarType.fixed,
      selectedItemColor: Colors.blue,
      unselectedItemColor: Colors.grey,
      currentIndex: validIndex,
      onTap: (index) => _onTabTapped(context, index),
      items: const [
        BottomNavigationBarItem(
          icon: Icon(Icons.router),
          label: 'Receivers',
        ),
        BottomNavigationBarItem(
          icon: Icon(Icons.map),
          label: 'Survey',
        ),
        BottomNavigationBarItem(
          icon: Icon(Icons.terminal),
          label: 'Terminal',
        ),
      ],
    );
  }

  void _onTabTapped(BuildContext context, int index) {
    switch (index) {
      case 0:
        // Navigate to Receivers (Main Screen)
        if (currentIndex != 0) {
          Navigator.of(context).pushAndRemoveUntil(
            MaterialPageRoute(builder: (context) => const MainScreen()),
            (route) => false,
          );
        }
        break;
      case 1:
        // Navigate to Survey screen (placeholder for now)
        if (currentIndex != 1) {
          Navigator.of(context).pushAndRemoveUntil(
            MaterialPageRoute(
              builder: (context) => Scaffold(
                appBar: AppBar(
                  title: const Text('Survey'),
                  backgroundColor: Theme.of(context).colorScheme.surface,
                  foregroundColor: Theme.of(context).colorScheme.onSurface,
                  bottom: PreferredSize(
                    preferredSize: const Size.fromHeight(1.0),
                    child: Container(
                      color: Colors.grey[300],
                      height: 1.0,
                    ),
                  ),
                ),
                body: const Center(
                  child: Column(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      Icon(Icons.map, size: 64, color: Colors.grey),
                      SizedBox(height: 16),
                      Text(
                        'Survey Screen',
                        style: TextStyle(fontSize: 24, fontWeight: FontWeight.bold),
                      ),
                      SizedBox(height: 8),
                      Text(
                        'Survey functionality coming soon',
                        style: TextStyle(fontSize: 16, color: Colors.grey),
                      ),
                    ],
                  ),
                ),
                bottomNavigationBar: const BottomNavigationWidget(currentIndex: 1),
              ),
            ),
            (route) => false,
          );
        }
        break;
      case 2:
        // Navigate to Terminal screen
        if (currentIndex != 2) {
          Navigator.of(context).pushAndRemoveUntil(
            MaterialPageRoute(
              builder: (context) => Scaffold(
                appBar: AppBar(
                  title: const Text('Terminal'),
                  backgroundColor: Theme.of(context).colorScheme.surface,
                  foregroundColor: Theme.of(context).colorScheme.onSurface,
                  bottom: PreferredSize(
                    preferredSize: const Size.fromHeight(1.0),
                    child: Container(
                      color: Colors.grey[300],
                      height: 1.0,
                    ),
                  ),
                ),
                body: const TerminalScreen(),
                bottomNavigationBar: const BottomNavigationWidget(currentIndex: 2),
              ),
            ),
            (route) => false,
          );
        }
        break;
    }
  }
}
