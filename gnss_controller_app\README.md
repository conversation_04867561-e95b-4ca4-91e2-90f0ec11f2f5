# Bedo GNSS Controller App

A comprehensive Flutter application for managing and configuring Bedo GNSS devices via Bluetooth communication. This app provides an intuitive interface for controlling GNSS receivers with real-time status monitoring and terminal-level interaction.

## Features

### 📱 Modern Interface Design

The app features a **vertical menu interface** similar to professional GNSS applications, with:
- **Main Dashboard**: Vertical list of functional modules with colored icons
- **Device Status Header**: Real-time connection status and device information
- **Mobile-First Design**: Responsive layout optimized for mobile devices
- **Intuitive Navigation**: Tap any menu item to access detailed configuration screens

### 🔧 Five Main Functional Modules

#### 1. Status (Connection Management)
- **Bluetooth Management**: Enable/disable Bluetooth functionality
- **Device Discovery**: Scan for and list available Bluetooth devices (Classic & BLE)
- **Connection Control**: Connect/disconnect to GNSS devices
- **Real-time Status**: Live connection status indicators
- **Device Information**: Display paired and discovered devices

#### 2. Corrections
- **Auto Restart**: Toggle automatic restart functionality
- **Status Indicators**: Real-time status for Bluetooth, NTRIP, and LoRa corrections
- **Correction Modes**:
  - **Bluetooth**: Receive corrections via Bluetooth
  - **NTRIP**: Internet-based correction service
  - **LoRa**: Radio-based correction communication
- **LoRa Configuration**:
  - Radio Channel selection (1-15)
  - Air Data Rate options (1.2-38.4 kbps)
  - Transmission Power settings (10-37 dBm)
- **Data Output**: Manages correction data output to connected devices

#### 3. GNSS Mode
- **Operation Modes**:
  - **PPP (Precise Point Positioning)**: High-accuracy positioning without base station
  - **Rover**: Mobile receiver with configurable update frequencies (1Hz, 5Hz, 10Hz)
  - **Average Base**: Base station mode with minimum time and accuracy settings
  - **Known Base**: Fixed base station with precise coordinates input
- **Dynamic Configuration**: Form fields adapt based on selected mode
- **Real-time Display**: Shows currently selected mode in main menu
- **Apply Settings**: Send configuration commands to connected device

#### 4. Settings
- **Cutoff Angle**: Satellite elevation mask (5°-30°)
- **Multipath Mitigation**: Enable/disable multipath error reduction
- **Anti-Jamming**: Toggle interference protection
- **Settings Summary**: Current configuration overview
- **Apply Changes**: Send settings to connected device

#### 5. Wi-Fi
- **Network Scanning**: Automatic discovery of available Wi-Fi networks
- **Signal Strength**: Real-time signal quality indicators
- **Secure Connection**: Password-protected network support
- **Connection Management**: Connect/disconnect from networks
- **Network Status**: Real-time connection status display

### 📊 Additional Features

#### Logging (via menu)
- **Recording Control**: Start/stop data logging
- **Auto Restart Logging**: Automatic logging restart option
- **Real-time Timer**: Live recording duration display
- **Log Management**:
  - View recorded log files
  - Download logs to device
  - Delete unwanted logs
  - File size and duration information

#### Terminal (via bottom navigation)
- **Command Interface**: Send custom commands to GNSS device
- **Real-time Monitoring**: Live display of device responses
- **Command History**: Scrollable terminal with timestamps
- **Clean Interface**: Streamlined command input without distracting buttons
- **Echo System**: All applied configurations are reflected in terminal
- **Connection Status**: Visual connection indicator

## Technical Architecture

### State Management
- **Provider Pattern**: Centralized state management using Provider package
- **Reactive UI**: Real-time updates across all screens
- **Data Persistence**: Local storage for configuration settings

### Bluetooth Communication
- **Classic Bluetooth**: Support for traditional Bluetooth connections
- **BLE Support**: Bluetooth Low Energy compatibility
- **Permission Handling**: Automatic permission requests
- **Error Handling**: Robust connection error management
- **Data Streaming**: Real-time data reception and processing

### UI/UX Design
- **Material 3**: Modern Material Design 3 components
- **Responsive Layout**: Adaptive design for different screen sizes
- **Dark/Light Theme**: Automatic theme switching support
- **Accessibility**: Screen reader and accessibility support
- **Intuitive Navigation**: Bottom navigation with clear icons

## Installation & Setup

### Prerequisites
- Flutter SDK (latest stable version)
- Android Studio / VS Code with Flutter extensions
- Android device with Bluetooth capability (for full functionality)

### Dependencies
```yaml
dependencies:
  flutter: sdk: flutter
  provider: ^6.1.2                    # State management
  flutter_bluetooth_serial: ^0.4.0    # Classic Bluetooth
  flutter_blue_plus: ^1.32.12         # BLE support
  permission_handler: ^11.3.1         # Permissions
  path_provider: ^2.1.3               # File management
  shared_preferences: ^2.2.3          # Local storage
  intl: ^0.19.0                       # Internationalization
```

### Installation Steps
1. **Clone the repository**:
   ```bash
   git clone <repository-url>
   cd gnss_controller_app
   ```

2. **Install dependencies**:
   ```bash
   flutter pub get
   ```

3. **Run the application**:
   ```bash
   flutter run
   ```

## Usage Guide

### Initial Setup
1. **Enable Bluetooth**: Use the Connection screen to enable Bluetooth
2. **Scan for Devices**: Search for available GNSS devices
3. **Connect**: Select and connect to your GNSS receiver
4. **Configure**: Set up GNSS mode, corrections, and settings
5. **Monitor**: Use the Terminal screen to monitor device communication

### Configuration Workflow
1. **GNSS Mode**: Choose operation mode (PPP, Rover, Base)
2. **Corrections**: Select correction source and configure parameters
3. **Settings**: Adjust cutoff angle, multipath, and anti-jamming
4. **Logging**: Start recording and manage log files
5. **Terminal**: Monitor real-time communication and send custom commands

### Command Echo System
All configuration changes are automatically echoed in the Terminal screen with timestamps:
```
[14:30:25] >> Sent: MODE ROVER 5HZ
[14:30:26] << Received: OK MODE SET
[14:30:30] >> Sent: CORRECTION LORA 5 9.6 20
[14:30:31] << Received: OK CORRECTION SET
```

## Platform Support

### Mobile Platforms
- **Android**: Full Bluetooth functionality
- **iOS**: BLE support (Classic Bluetooth limited by iOS restrictions)

### Desktop/Web
- **Windows/macOS/Linux**: UI testing (Bluetooth functionality limited)
- **Web**: UI demonstration (no Bluetooth support)

## Development

### Project Structure
```
lib/
├── main.dart                 # App entry point
├── models/
│   └── gnss_models.dart     # Data models and enums
├── providers/
│   ├── bluetooth_provider.dart    # Bluetooth communication
│   ├── gnss_provider.dart         # GNSS configuration
│   └── terminal_provider.dart     # Terminal messages
├── screens/
│   ├── main_screen.dart           # Main navigation
│   ├── connection_screen.dart     # Bluetooth connection
│   ├── gnss_mode_screen.dart      # GNSS mode configuration
│   ├── corrections_screen.dart    # Correction settings
│   ├── settings_screen.dart       # Device settings
│   ├── logging_screen.dart        # Data logging
│   └── terminal_screen.dart       # Command terminal
└── widgets/                       # Reusable UI components
```

### Key Features Implementation
- **Real-time Updates**: Provider pattern ensures UI updates when data changes
- **Command Generation**: Dynamic command generation based on configuration
- **Error Handling**: Comprehensive error handling for Bluetooth operations
- **Data Validation**: Input validation for all configuration fields
- **Responsive Design**: Adaptive layouts for different screen sizes

## Contributing

1. Fork the repository
2. Create a feature branch (`git checkout -b feature/amazing-feature`)
3. Commit your changes (`git commit -m 'Add amazing feature'`)
4. Push to the branch (`git push origin feature/amazing-feature`)
5. Open a Pull Request

## License

This project is licensed under the MIT License - see the LICENSE file for details.

## Support

For support and questions:
- Create an issue in the repository
- Check the Flutter documentation for general Flutter questions
- Review the Bluetooth plugin documentation for connectivity issues

## Acknowledgments

- Flutter team for the excellent framework
- Bluetooth plugin maintainers for reliable connectivity
- Material Design team for the beautiful UI components
- GNSS community for technical specifications and standards
