import 'dart:async';
import 'dart:convert';
import 'dart:typed_data';
import 'package:flutter/foundation.dart';
import 'package:flutter_bluetooth_serial/flutter_bluetooth_serial.dart';
import 'package:permission_handler/permission_handler.dart';
import '../models/gnss_models.dart';

class BluetoothProvider extends ChangeNotifier {
  BluetoothConnection? _connection;
  ConnectionStatus _connectionStatus = ConnectionStatus.disconnected;
  List<GnssBluetoothDevice> _availableDevices = [];
  GnssBluetoothDevice? _connectedDevice;
  bool _isBluetoothEnabled = false;
  StreamSubscription<Uint8List>? _dataSubscription;
  final StreamController<String> _dataStreamController = StreamController<String>.broadcast();

  // Getters
  ConnectionStatus get connectionStatus => _connectionStatus;
  List<GnssBluetoothDevice> get availableDevices => _availableDevices;
  GnssBluetoothDevice? get connectedDevice => _connectedDevice;
  bool get isBluetoothEnabled => _isBluetoothEnabled;
  Stream<String> get dataStream => _dataStreamController.stream;
  bool get isConnected => _connectionStatus == ConnectionStatus.connected;

  BluetoothProvider() {
    _initializeBluetooth();
  }

  Future<void> _initializeBluetooth() async {
    try {
      // Check if Bluetooth is enabled
      _isBluetoothEnabled = await FlutterBluetoothSerial.instance.isEnabled ?? false;
      notifyListeners();

      if (_isBluetoothEnabled) {
        await _loadPairedDevices();
      }
    } catch (e) {
      debugPrint('Error initializing Bluetooth: $e');
    }
  }

  Future<bool> enableBluetooth() async {
    try {
      // Request permissions
      final bluetoothPermission = await Permission.bluetooth.request();
      final bluetoothConnectPermission = await Permission.bluetoothConnect.request();
      final bluetoothScanPermission = await Permission.bluetoothScan.request();

      if (bluetoothPermission.isDenied ||
          bluetoothConnectPermission.isDenied ||
          bluetoothScanPermission.isDenied) {
        return false;
      }

      // Enable Bluetooth
      final result = await FlutterBluetoothSerial.instance.requestEnable();
      _isBluetoothEnabled = result ?? false;

      if (_isBluetoothEnabled) {
        await _loadPairedDevices();
      }

      notifyListeners();
      return _isBluetoothEnabled;
    } catch (e) {
      debugPrint('Error enabling Bluetooth: $e');
      return false;
    }
  }

  Future<void> _loadPairedDevices() async {
    try {
      final bondedDevices = await FlutterBluetoothSerial.instance.getBondedDevices();
      _availableDevices = bondedDevices.map((device) => GnssBluetoothDevice(
        name: device.name ?? 'Unknown Device',
        address: device.address,
        isPaired: true,
        type: ConnectionType.classic,
      )).toList();
      notifyListeners();
    } catch (e) {
      debugPrint('Error loading paired devices: $e');
    }
  }

  Future<void> scanForDevices() async {
    if (!_isBluetoothEnabled) {
      await enableBluetooth();
    }

    try {
      _availableDevices.clear();
      notifyListeners();

      // Load paired devices first
      await _loadPairedDevices();

      // Start discovery for new devices
      final discoveryStream = FlutterBluetoothSerial.instance.startDiscovery();

      await for (BluetoothDiscoveryResult result in discoveryStream) {
        final device = GnssBluetoothDevice(
          name: result.device.name ?? 'Unknown Device',
          address: result.device.address,
          isPaired: result.device.isBonded,
          type: ConnectionType.classic,
        );

        // Add device if not already in list
        if (!_availableDevices.any((d) => d.address == device.address)) {
          _availableDevices.add(device);
          notifyListeners();
        }
      }
    } catch (e) {
      debugPrint('Error scanning for devices: $e');
    }
  }

  Future<bool> connectToDevice(GnssBluetoothDevice device) async {
    if (_connectionStatus == ConnectionStatus.connecting) {
      return false;
    }

    try {
      _connectionStatus = ConnectionStatus.connecting;
      notifyListeners();

      _connection = await BluetoothConnection.toAddress(device.address);

      if (_connection?.isConnected == true) {
        _connectedDevice = device;
        _connectionStatus = ConnectionStatus.connected;

        // Listen for incoming data
        _dataSubscription = _connection!.input!.listen(
          (Uint8List data) {
            final message = utf8.decode(data);
            _dataStreamController.add(message);
          },
          onError: (error) {
            debugPrint('Bluetooth data error: $error');
            disconnect();
          },
          onDone: () {
            debugPrint('Bluetooth connection closed');
            disconnect();
          },
        );

        notifyListeners();
        return true;
      } else {
        _connectionStatus = ConnectionStatus.error;
        notifyListeners();
        return false;
      }
    } catch (e) {
      debugPrint('Error connecting to device: $e');
      _connectionStatus = ConnectionStatus.error;
      notifyListeners();
      return false;
    }
  }

  Future<void> disconnect() async {
    try {
      await _dataSubscription?.cancel();
      _dataSubscription = null;

      await _connection?.close();
      _connection = null;

      _connectedDevice = null;
      _connectionStatus = ConnectionStatus.disconnected;
      notifyListeners();
    } catch (e) {
      debugPrint('Error disconnecting: $e');
    }
  }

  Future<bool> sendCommand(String command) async {
    if (_connection?.isConnected != true) {
      return false;
    }

    try {
      final data = utf8.encode('$command\r\n');
      _connection!.output.add(Uint8List.fromList(data));
      await _connection!.output.allSent;
      return true;
    } catch (e) {
      debugPrint('Error sending command: $e');
      return false;
    }
  }

  @override
  void dispose() {
    _dataSubscription?.cancel();
    _connection?.close();
    _dataStreamController.close();
    super.dispose();
  }
}
