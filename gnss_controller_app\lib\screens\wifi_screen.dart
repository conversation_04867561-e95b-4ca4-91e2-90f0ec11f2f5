import 'package:flutter/material.dart';

class WiFiScreen extends StatefulWidget {
  const WiFiScreen({super.key});

  @override
  State<WiFiScreen> createState() => _WiFiScreenState();
}

class _WiFiScreenState extends State<WiFiScreen> {
  bool _isScanning = false;
  List<WiFiNetwork> _availableNetworks = [];

  @override
  void initState() {
    super.initState();
    _scanForNetworks();
  }

  Future<void> _scanForNetworks() async {
    setState(() {
      _isScanning = true;
    });

    // Simulate network scanning
    await Future.delayed(const Duration(seconds: 2));

    setState(() {
      _availableNetworks = [
        WiFiNetwork('Bedo_GNSS_AP', -45, true, true),
        WiFiNetwork('Home_WiFi', -55, false, true),
        WiFiNetwork('Office_Network', -65, false, true),
        WiFiNetwork('Guest_WiFi', -70, false, false),
        WiFiNetwork('Mobile_Hotspot', -75, false, true),
        WiFiNetwork('Neighbor_WiFi', -80, false, true),
      ];
      _isScanning = false;
    });
  }

  void _connectToNetwork(WiFiNetwork network) {
    if (network.isSecured) {
      _showPasswordDialog(network);
    } else {
      _performConnection(network, '');
    }
  }

  void _showPasswordDialog(WiFiNetwork network) {
    final passwordController = TextEditingController();

    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text('Connect to ${network.name}'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            const Text('This network requires a password:'),
            const SizedBox(height: 16),
            TextField(
              controller: passwordController,
              obscureText: true,
              decoration: const InputDecoration(
                labelText: 'Password',
                border: OutlineInputBorder(),
              ),
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Cancel'),
          ),
          ElevatedButton(
            onPressed: () {
              Navigator.of(context).pop();
              _performConnection(network, passwordController.text);
            },
            child: const Text('Connect'),
          ),
        ],
      ),
    );
  }

  void _performConnection(WiFiNetwork network, String password) {
    // Simulate connection
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text('Connecting to ${network.name}...'),
        duration: const Duration(seconds: 2),
      ),
    );

    // Update network status after connection
    Future.delayed(const Duration(seconds: 2), () {
      setState(() {
        for (var net in _availableNetworks) {
          net.isConnected = net.name == network.name;
        }
      });

      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('Connected to ${network.name}'),
          backgroundColor: Colors.green,
        ),
      );
    });
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Header with refresh button
            Row(
              children: [
                const Icon(Icons.wifi, color: Colors.orange),
                const SizedBox(width: 8),
                Text(
                  'Available Networks',
                  style: Theme.of(context).textTheme.titleMedium,
                ),
                const Spacer(),
                IconButton(
                  onPressed: _isScanning ? null : _scanForNetworks,
                  icon: _isScanning
                      ? const SizedBox(
                          width: 20,
                          height: 20,
                          child: CircularProgressIndicator(strokeWidth: 2),
                        )
                      : const Icon(Icons.refresh),
                ),
              ],
            ),

            const SizedBox(height: 16),

            // Networks list
            Expanded(
              child: _isScanning && _availableNetworks.isEmpty
                  ? const Center(
                      child: Column(
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          CircularProgressIndicator(),
                          SizedBox(height: 16),
                          Text('Scanning for networks...'),
                        ],
                      ),
                    )
                  : ListView.builder(
                      itemCount: _availableNetworks.length,
                      itemBuilder: (context, index) {
                        final network = _availableNetworks[index];
                        return Card(
                          child: ListTile(
                            leading: Icon(
                              _getSignalIcon(network.signalStrength),
                              color: _getSignalColor(network.signalStrength),
                            ),
                            title: Text(network.name),
                            subtitle: Row(
                              children: [
                                if (network.isSecured)
                                  const Icon(Icons.lock, size: 16, color: Colors.grey),
                                if (network.isSecured) const SizedBox(width: 4),
                                Text(_getSignalText(network.signalStrength)),
                              ],
                            ),
                            trailing: network.isConnected
                                ? const Icon(Icons.check_circle, color: Colors.green)
                                : IconButton(
                                    icon: const Icon(Icons.wifi),
                                    onPressed: () => _connectToNetwork(network),
                                  ),
                            onTap: network.isConnected ? null : () => _connectToNetwork(network),
                          ),
                        );
                      },
                    ),
            ),
          ],
        ),
      ),
    );
  }

  IconData _getSignalIcon(int strength) {
    if (strength > -50) return Icons.wifi;
    if (strength > -60) return Icons.wifi;
    if (strength > -70) return Icons.wifi;
    return Icons.wifi;
  }

  Color _getSignalColor(int strength) {
    if (strength > -50) return Colors.green;
    if (strength > -60) return Colors.orange;
    if (strength > -70) return Colors.orange;
    return Colors.red;
  }

  String _getSignalText(int strength) {
    if (strength > -50) return 'Excellent';
    if (strength > -60) return 'Good';
    if (strength > -70) return 'Fair';
    return 'Weak';
  }
}

class WiFiNetwork {
  final String name;
  final int signalStrength;
  bool isConnected;
  final bool isSecured;

  WiFiNetwork(this.name, this.signalStrength, this.isConnected, this.isSecured);
}
