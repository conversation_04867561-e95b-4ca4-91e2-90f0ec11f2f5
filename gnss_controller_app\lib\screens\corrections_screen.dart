import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../providers/gnss_provider.dart';
import '../providers/bluetooth_provider.dart';
import '../providers/terminal_provider.dart';
import '../models/gnss_models.dart';

class CorrectionsScreen extends StatefulWidget {
  const CorrectionsScreen({super.key});

  @override
  State<CorrectionsScreen> createState() => _CorrectionsScreenState();
}

class _CorrectionsScreenState extends State<CorrectionsScreen> {
  Future<void> _applyConfiguration() async {
    final gnss = context.read<GnssProvider>();
    final bluetooth = context.read<BluetoothProvider>();
    final terminal = context.read<TerminalProvider>();

    final command = gnss.generateCorrectionCommand();
    terminal.addCommandEcho(command);

    if (bluetooth.isConnected) {
      final success = await bluetooth.sendCommand(command);
      if (success) {
        terminal.addSystemMessage('Correction configuration sent successfully');
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(content: Text('Configuration applied successfully')),
        );
      } else {
        terminal.addSystemMessage('Failed to send correction configuration');
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(content: Text('Failed to apply configuration')),
        );
      }
    } else {
      terminal.addSystemMessage('No device connected - configuration saved locally');
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(content: Text('Configuration saved (no device connected)')),
      );
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: Consumer<GnssProvider>(
        builder: (context, gnss, child) {
          return SingleChildScrollView(
            padding: const EdgeInsets.all(16.0),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // Auto Restart Card
                Card(
                  child: Padding(
                    padding: const EdgeInsets.all(16.0),
                    child: Row(
                      children: [
                        const Icon(Icons.refresh),
                        const SizedBox(width: 8),
                        const Text('Auto Restart'),
                        const Spacer(),
                        Switch(
                          value: gnss.correctionConfig.autoRestart,
                          onChanged: (value) {
                            gnss.updateAutoRestart(value);
                          },
                        ),
                      ],
                    ),
                  ),
                ),

                const SizedBox(height: 16),

                // Status Card
                Card(
                  child: Padding(
                    padding: const EdgeInsets.all(16.0),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          'Correction Status',
                          style: Theme.of(context).textTheme.titleMedium,
                        ),
                        const SizedBox(height: 16),
                        Row(
                          mainAxisAlignment: MainAxisAlignment.spaceAround,
                          children: [
                            _buildStatusIndicator(
                              'Bluetooth',
                              gnss.correctionStatus.bluetoothActive,
                              Icons.bluetooth,
                            ),
                            _buildStatusIndicator(
                              'NTRIP',
                              gnss.correctionStatus.ntripActive,
                              Icons.wifi,
                            ),
                            _buildStatusIndicator(
                              'LoRa',
                              gnss.correctionStatus.loraActive,
                              Icons.radio,
                            ),
                          ],
                        ),
                      ],
                    ),
                  ),
                ),

                const SizedBox(height: 16),

                // Mode Selection Card
                Card(
                  child: Padding(
                    padding: const EdgeInsets.all(16.0),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          'Correction Mode',
                          style: Theme.of(context).textTheme.titleMedium,
                        ),
                        const SizedBox(height: 16),
                        // Mobile-friendly correction mode selection
                        Column(
                          children: [
                            _buildModeOption(
                              context,
                              CorrectionMode.bluetooth,
                              'Bluetooth',
                              Icons.bluetooth,
                              gnss.correctionConfig.mode == CorrectionMode.bluetooth,
                              () => gnss.updateCorrectionMode(CorrectionMode.bluetooth),
                            ),
                            const SizedBox(height: 8),
                            _buildModeOption(
                              context,
                              CorrectionMode.ntrip,
                              'NTRIP',
                              Icons.wifi,
                              gnss.correctionConfig.mode == CorrectionMode.ntrip,
                              () => gnss.updateCorrectionMode(CorrectionMode.ntrip),
                            ),
                            const SizedBox(height: 8),
                            _buildModeOption(
                              context,
                              CorrectionMode.lora,
                              'LoRa',
                              Icons.radio,
                              gnss.correctionConfig.mode == CorrectionMode.lora,
                              () => gnss.updateCorrectionMode(CorrectionMode.lora),
                            ),
                          ],
                        ),
                      ],
                    ),
                  ),
                ),

                const SizedBox(height: 16),

                // LoRa Configuration (only visible when LoRa is selected)
                if (gnss.correctionConfig.mode == CorrectionMode.lora) ...[
                  Card(
                      child: Padding(
                        padding: const EdgeInsets.all(16.0),
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Row(
                              children: [
                                const Icon(Icons.radio),
                                const SizedBox(width: 8),
                                Text(
                                  'LoRa Configuration',
                                  style: Theme.of(context).textTheme.titleMedium,
                                ),
                              ],
                            ),
                            const SizedBox(height: 16),

                            // Radio Channel
                            DropdownButtonFormField<int>(
                              value: gnss.correctionConfig.loraSettings.channel,
                              decoration: const InputDecoration(
                                labelText: 'Radio Channel',
                                border: OutlineInputBorder(),
                              ),
                              items: GnssProvider.loraChannelOptions.map((channel) {
                                return DropdownMenuItem(
                                  value: channel,
                                  child: Text('Channel $channel'),
                                );
                              }).toList(),
                              onChanged: (channel) {
                                if (channel != null) {
                                  gnss.updateLoRaChannel(channel);
                                }
                              },
                            ),

                            const SizedBox(height: 16),

                            // Air Data Rate
                            DropdownButtonFormField<double>(
                              value: gnss.correctionConfig.loraSettings.airDataRate,
                              decoration: const InputDecoration(
                                labelText: 'Air Data Rate',
                                border: OutlineInputBorder(),
                                suffixText: 'kbps',
                              ),
                              items: GnssProvider.airDataRateOptions.map((rate) {
                                return DropdownMenuItem(
                                  value: rate,
                                  child: Text('${rate} kbps'),
                                );
                              }).toList(),
                              onChanged: (rate) {
                                if (rate != null) {
                                  gnss.updateLoRaAirDataRate(rate);
                                }
                              },
                            ),

                            const SizedBox(height: 16),

                            // Transmission Power
                            DropdownButtonFormField<int>(
                              value: gnss.correctionConfig.loraSettings.transmissionPower,
                              decoration: const InputDecoration(
                                labelText: 'Transmission Power',
                                border: OutlineInputBorder(),
                                suffixText: 'dBm',
                              ),
                              items: GnssProvider.transmissionPowerOptions.map((power) {
                                return DropdownMenuItem(
                                  value: power,
                                  child: Text('$power dBm'),
                                );
                              }).toList(),
                              onChanged: (power) {
                                if (power != null) {
                                  gnss.updateLoRaTransmissionPower(power);
                                }
                              },
                            ),
                          ],
                        ),
                      ),
                    ),
                ] else ...[
                  Card(
                    child: Padding(
                      padding: const EdgeInsets.all(16.0),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            _getCorrectionModeDescription(gnss.correctionConfig.mode),
                            style: Theme.of(context).textTheme.titleMedium,
                          ),
                          const SizedBox(height: 8),
                          Text(
                            _getCorrectionModeDetails(gnss.correctionConfig.mode),
                            style: const TextStyle(color: Colors.grey),
                          ),
                        ],
                      ),
                    ),
                  ),
                ],

                const SizedBox(height: 16),

                // Apply Button
                SizedBox(
                  width: double.infinity,
                  child: ElevatedButton.icon(
                    onPressed: _applyConfiguration,
                    icon: const Icon(Icons.check),
                    label: const Text('Apply Configuration'),
                    style: ElevatedButton.styleFrom(
                      padding: const EdgeInsets.symmetric(vertical: 16),
                    ),
                  ),
                ),
              ],
            ),
          );
        },
      ),
    );
  }

  Widget _buildModeOption(
    BuildContext context,
    CorrectionMode mode,
    String label,
    IconData icon,
    bool isSelected,
    VoidCallback onTap,
  ) {
    return InkWell(
      onTap: onTap,
      borderRadius: BorderRadius.circular(8),
      child: Container(
        width: double.infinity,
        padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
        decoration: BoxDecoration(
          border: Border.all(
            color: isSelected ? Theme.of(context).primaryColor : Colors.grey.shade300,
            width: isSelected ? 2 : 1,
          ),
          borderRadius: BorderRadius.circular(8),
          color: isSelected ? Theme.of(context).primaryColor.withOpacity(0.1) : null,
        ),
        child: Row(
          children: [
            Icon(
              icon,
              color: isSelected ? Theme.of(context).primaryColor : Colors.grey.shade600,
              size: 24,
            ),
            const SizedBox(width: 12),
            Expanded(
              child: Text(
                label,
                style: TextStyle(
                  fontSize: 16,
                  fontWeight: isSelected ? FontWeight.w600 : FontWeight.normal,
                  color: isSelected ? Theme.of(context).primaryColor : Colors.grey.shade800,
                ),
              ),
            ),
            if (isSelected)
              Icon(
                Icons.check_circle,
                color: Theme.of(context).primaryColor,
                size: 20,
              ),
          ],
        ),
      ),
    );
  }

  Widget _buildStatusIndicator(String label, bool isActive, IconData icon) {
    return Column(
      children: [
        Icon(
          icon,
          color: isActive ? Colors.green : Colors.grey,
          size: 32,
        ),
        const SizedBox(height: 4),
        Text(
          label,
          style: const TextStyle(fontSize: 12),
        ),
        const SizedBox(height: 2),
        Container(
          width: 8,
          height: 8,
          decoration: BoxDecoration(
            shape: BoxShape.circle,
            color: isActive ? Colors.green : Colors.grey,
          ),
        ),
      ],
    );
  }

  String _getCorrectionModeDescription(CorrectionMode mode) {
    switch (mode) {
      case CorrectionMode.bluetooth:
        return 'Bluetooth Corrections';
      case CorrectionMode.ntrip:
        return 'NTRIP Corrections';
      case CorrectionMode.lora:
        return 'LoRa Corrections';
    }
  }

  String _getCorrectionModeDetails(CorrectionMode mode) {
    switch (mode) {
      case CorrectionMode.bluetooth:
        return 'Receive correction data via Bluetooth from another GNSS device or base station.';
      case CorrectionMode.ntrip:
        return 'Receive correction data over internet using NTRIP protocol from a correction service provider.';
      case CorrectionMode.lora:
        return 'Receive correction data via LoRa radio communication from a nearby base station.';
    }
  }
}
