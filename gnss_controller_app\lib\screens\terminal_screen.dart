import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../providers/terminal_provider.dart';
import '../providers/bluetooth_provider.dart';

class TerminalScreen extends StatefulWidget {
  const TerminalScreen({super.key});

  @override
  State<TerminalScreen> createState() => _TerminalScreenState();
}

class _TerminalScreenState extends State<TerminalScreen> {
  final TextEditingController _commandController = TextEditingController();
  final ScrollController _scrollController = ScrollController();

  @override
  void initState() {
    super.initState();
    // Listen for incoming Bluetooth data
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _setupBluetoothListener();
    });
  }

  void _setupBluetoothListener() {
    final bluetooth = context.read<BluetoothProvider>();
    final terminal = context.read<TerminalProvider>();
    
    bluetooth.dataStream.listen((data) {
      terminal.addResponse(data.trim());
      _scrollToBottom();
    });
  }

  void _scrollToBottom() {
    WidgetsBinding.instance.addPostFrameCallback((_) {
      if (_scrollController.hasClients) {
        _scrollController.animateTo(
          _scrollController.position.maxScrollExtent,
          duration: const Duration(milliseconds: 300),
          curve: Curves.easeOut,
        );
      }
    });
  }

  Future<void> _sendCommand() async {
    final command = _commandController.text.trim();
    if (command.isEmpty) return;

    final bluetooth = context.read<BluetoothProvider>();
    final terminal = context.read<TerminalProvider>();

    // Add command to terminal
    terminal.addCommandEcho(command);

    if (bluetooth.isConnected) {
      final success = await bluetooth.sendCommand(command);
      if (!success) {
        terminal.addSystemMessage('Failed to send command');
      }
    } else {
      terminal.addSystemMessage('No device connected');
    }

    _commandController.clear();
    _scrollToBottom();
  }

  void _clearTerminal() {
    context.read<TerminalProvider>().clearMessages();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: Consumer<TerminalProvider>(
        builder: (context, terminal, child) {
          return Column(
            children: [
              // Terminal Header
              Container(
                padding: const EdgeInsets.all(16.0),
                decoration: BoxDecoration(
                  color: Theme.of(context).colorScheme.surfaceVariant,
                  border: Border(
                    bottom: BorderSide(
                      color: Theme.of(context).colorScheme.outline,
                    ),
                  ),
                ),
                child: Row(
                  children: [
                    const Icon(Icons.terminal),
                    const SizedBox(width: 8),
                    Text(
                      'Terminal',
                      style: Theme.of(context).textTheme.titleMedium,
                    ),
                    const Spacer(),
                    IconButton(
                      onPressed: _clearTerminal,
                      icon: const Icon(Icons.clear_all),
                      tooltip: 'Clear Terminal',
                    ),
                    Consumer<BluetoothProvider>(
                      builder: (context, bluetooth, child) {
                        return Container(
                          padding: const EdgeInsets.symmetric(
                            horizontal: 8,
                            vertical: 4,
                          ),
                          decoration: BoxDecoration(
                            color: bluetooth.isConnected ? Colors.green : Colors.red,
                            borderRadius: BorderRadius.circular(12),
                          ),
                          child: Text(
                            bluetooth.isConnected ? 'CONNECTED' : 'DISCONNECTED',
                            style: const TextStyle(
                              color: Colors.white,
                              fontSize: 12,
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                        );
                      },
                    ),
                  ],
                ),
              ),
              
              // Terminal Output
              Expanded(
                child: Container(
                  color: Colors.black,
                  child: terminal.messages.isEmpty
                    ? const Center(
                        child: Text(
                          'Terminal ready...\nCommands and responses will appear here.',
                          style: TextStyle(
                            color: Colors.green,
                            fontFamily: 'monospace',
                          ),
                          textAlign: TextAlign.center,
                        ),
                      )
                    : ListView.builder(
                        controller: _scrollController,
                        padding: const EdgeInsets.all(8.0),
                        itemCount: terminal.messages.length,
                        itemBuilder: (context, index) {
                          final message = terminal.messages[index];
                          return Padding(
                            padding: const EdgeInsets.symmetric(vertical: 1.0),
                            child: RichText(
                              text: TextSpan(
                                style: const TextStyle(
                                  fontFamily: 'monospace',
                                  fontSize: 12,
                                ),
                                children: [
                                  TextSpan(
                                    text: _formatTimestamp(message.timestamp),
                                    style: const TextStyle(color: Colors.grey),
                                  ),
                                  TextSpan(
                                    text: ' ${message.isCommand ? '>>' : '<<'} ',
                                    style: TextStyle(
                                      color: message.isCommand ? Colors.yellow : Colors.cyan,
                                      fontWeight: FontWeight.bold,
                                    ),
                                  ),
                                  TextSpan(
                                    text: message.message,
                                    style: TextStyle(
                                      color: message.isCommand ? Colors.white : Colors.green,
                                    ),
                                  ),
                                ],
                              ),
                            ),
                          );
                        },
                      ),
                ),
              ),
              
              // Command Input
              Container(
                padding: const EdgeInsets.all(16.0),
                decoration: BoxDecoration(
                  color: Theme.of(context).colorScheme.surface,
                  border: Border(
                    top: BorderSide(
                      color: Theme.of(context).colorScheme.outline,
                    ),
                  ),
                ),
                child: Row(
                  children: [
                    Expanded(
                      child: TextField(
                        controller: _commandController,
                        decoration: const InputDecoration(
                          hintText: 'Enter command...',
                          border: OutlineInputBorder(),
                          prefixIcon: Icon(Icons.keyboard),
                        ),
                        onSubmitted: (_) => _sendCommand(),
                        textInputAction: TextInputAction.send,
                      ),
                    ),
                    const SizedBox(width: 8),
                    ElevatedButton.icon(
                      onPressed: _sendCommand,
                      icon: const Icon(Icons.send),
                      label: const Text('Send'),
                    ),
                  ],
                ),
              ),
              
              // Quick Commands
              Container(
                padding: const EdgeInsets.symmetric(horizontal: 16.0, vertical: 8.0),
                child: SingleChildScrollView(
                  scrollDirection: Axis.horizontal,
                  child: Row(
                    children: [
                      _buildQuickCommand('STATUS', 'Get device status'),
                      const SizedBox(width: 8),
                      _buildQuickCommand('VERSION', 'Get firmware version'),
                      const SizedBox(width: 8),
                      _buildQuickCommand('RESET', 'Reset device'),
                      const SizedBox(width: 8),
                      _buildQuickCommand('HELP', 'Show available commands'),
                    ],
                  ),
                ),
              ),
            ],
          );
        },
      ),
    );
  }

  Widget _buildQuickCommand(String command, String tooltip) {
    return Tooltip(
      message: tooltip,
      child: OutlinedButton(
        onPressed: () {
          _commandController.text = command;
          _sendCommand();
        },
        style: OutlinedButton.styleFrom(
          padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
        ),
        child: Text(
          command,
          style: const TextStyle(fontSize: 12),
        ),
      ),
    );
  }

  String _formatTimestamp(DateTime timestamp) {
    return '${timestamp.hour.toString().padLeft(2, '0')}:'
           '${timestamp.minute.toString().padLeft(2, '0')}:'
           '${timestamp.second.toString().padLeft(2, '0')}';
  }

  @override
  void dispose() {
    _commandController.dispose();
    _scrollController.dispose();
    super.dispose();
  }
}
