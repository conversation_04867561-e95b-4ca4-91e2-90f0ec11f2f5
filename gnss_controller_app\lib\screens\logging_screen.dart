import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:intl/intl.dart';
import '../providers/gnss_provider.dart';
import '../providers/bluetooth_provider.dart';
import '../providers/terminal_provider.dart';
import '../models/gnss_models.dart';

class LoggingScreen extends StatefulWidget {
  const LoggingScreen({super.key});

  @override
  State<LoggingScreen> createState() => _LoggingScreenState();
}

class _LoggingScreenState extends State<LoggingScreen> {
  final DateFormat _dateFormat = DateFormat('MMM dd, yyyy');
  final DateFormat _timeFormat = DateFormat('HH:mm:ss');

  Future<void> _toggleLogging() async {
    final gnss = context.read<GnssProvider>();
    final bluetooth = context.read<BluetoothProvider>();
    final terminal = context.read<TerminalProvider>();

    final isRecording = gnss.loggingStatus == LoggingStatus.recording;
    final command = gnss.generateLoggingCommand(!isRecording);
    
    terminal.addCommandEcho(command);

    if (bluetooth.isConnected) {
      final success = await bluetooth.sendCommand(command);
      if (success) {
        if (isRecording) {
          gnss.stopLogging();
          terminal.addSystemMessage('Logging stopped');
        } else {
          gnss.startLogging();
          terminal.addSystemMessage('Logging started');
        }
      } else {
        terminal.addSystemMessage('Failed to send logging command');
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(content: Text('Failed to send command')),
        );
      }
    } else {
      // Allow local testing without device
      if (isRecording) {
        gnss.stopLogging();
        terminal.addSystemMessage('Logging stopped (no device connected)');
      } else {
        gnss.startLogging();
        terminal.addSystemMessage('Logging started (no device connected)');
      }
    }
  }

  void _deleteLog(LogEntry entry) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Delete Log'),
        content: Text('Are you sure you want to delete ${entry.fileName}?'),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Cancel'),
          ),
          TextButton(
            onPressed: () {
              context.read<GnssProvider>().deleteLogEntry(entry);
              context.read<TerminalProvider>().addSystemMessage('Log deleted: ${entry.fileName}');
              Navigator.of(context).pop();
            },
            child: const Text('Delete'),
          ),
        ],
      ),
    );
  }

  void _downloadLog(LogEntry entry) {
    // In a real app, this would trigger file download
    context.read<TerminalProvider>().addSystemMessage('Download started: ${entry.fileName}');
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(content: Text('Downloading ${entry.fileName}...')),
    );
  }

  String _formatDuration(Duration duration) {
    final hours = duration.inHours;
    final minutes = duration.inMinutes.remainder(60);
    final seconds = duration.inSeconds.remainder(60);
    
    if (hours > 0) {
      return '${hours}h ${minutes}m ${seconds}s';
    } else if (minutes > 0) {
      return '${minutes}m ${seconds}s';
    } else {
      return '${seconds}s';
    }
  }

  String _formatFileSize(int bytes) {
    if (bytes < 1024) {
      return '$bytes B';
    } else if (bytes < 1024 * 1024) {
      return '${(bytes / 1024).toStringAsFixed(1)} KB';
    } else {
      return '${(bytes / (1024 * 1024)).toStringAsFixed(1)} MB';
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: Consumer<GnssProvider>(
        builder: (context, gnss, child) {
          return Padding(
            padding: const EdgeInsets.all(16.0),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // Auto Restart Card
                Card(
                  child: Padding(
                    padding: const EdgeInsets.all(16.0),
                    child: Row(
                      children: [
                        const Icon(Icons.refresh),
                        const SizedBox(width: 8),
                        const Text('Auto Restart Logging'),
                        const Spacer(),
                        Switch(
                          value: gnss.autoRestartLogging,
                          onChanged: (value) {
                            gnss.updateAutoRestartLogging(value);
                          },
                        ),
                      ],
                    ),
                  ),
                ),
                
                const SizedBox(height: 16),
                
                // Logging Control Card
                Card(
                  child: Padding(
                    padding: const EdgeInsets.all(16.0),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Row(
                          children: [
                            Icon(
                              gnss.loggingStatus == LoggingStatus.recording
                                ? Icons.fiber_manual_record
                                : Icons.stop_circle,
                              color: gnss.loggingStatus == LoggingStatus.recording
                                ? Colors.red
                                : Colors.grey,
                            ),
                            const SizedBox(width: 8),
                            Text(
                              'Logging Status',
                              style: Theme.of(context).textTheme.titleMedium,
                            ),
                          ],
                        ),
                        const SizedBox(height: 16),
                        
                        if (gnss.loggingStatus == LoggingStatus.recording) ...[
                          Text(
                            'Recording: ${_formatDuration(gnss.loggingDuration)}',
                            style: const TextStyle(
                              fontSize: 18,
                              fontWeight: FontWeight.bold,
                              color: Colors.red,
                            ),
                          ),
                          const SizedBox(height: 16),
                        ],
                        
                        SizedBox(
                          width: double.infinity,
                          child: ElevatedButton.icon(
                            onPressed: _toggleLogging,
                            icon: Icon(
                              gnss.loggingStatus == LoggingStatus.recording
                                ? Icons.stop
                                : Icons.play_arrow,
                            ),
                            label: Text(
                              gnss.loggingStatus == LoggingStatus.recording
                                ? 'Stop Recording'
                                : 'Start Recording',
                            ),
                            style: ElevatedButton.styleFrom(
                              backgroundColor: gnss.loggingStatus == LoggingStatus.recording
                                ? Colors.red
                                : Colors.green,
                              foregroundColor: Colors.white,
                              padding: const EdgeInsets.symmetric(vertical: 16),
                            ),
                          ),
                        ),
                      ],
                    ),
                  ),
                ),
                
                const SizedBox(height: 16),
                
                // Recorded Logs Section
                Row(
                  children: [
                    const Icon(Icons.folder),
                    const SizedBox(width: 8),
                    Text(
                      'Recorded Logs',
                      style: Theme.of(context).textTheme.titleMedium,
                    ),
                    const Spacer(),
                    if (gnss.logEntries.isNotEmpty)
                      TextButton.icon(
                        onPressed: () {
                          // Show filter/sort options
                        },
                        icon: const Icon(Icons.filter_list),
                        label: const Text('Filter'),
                      ),
                  ],
                ),
                
                const SizedBox(height: 8),
                
                // Logs List
                Expanded(
                  child: gnss.logEntries.isEmpty
                    ? const Center(
                        child: Column(
                          mainAxisAlignment: MainAxisAlignment.center,
                          children: [
                            Icon(
                              Icons.folder_open,
                              size: 64,
                              color: Colors.grey,
                            ),
                            SizedBox(height: 16),
                            Text(
                              'No logs recorded yet',
                              style: TextStyle(
                                fontSize: 16,
                                color: Colors.grey,
                              ),
                            ),
                            SizedBox(height: 8),
                            Text(
                              'Start recording to create log files',
                              style: TextStyle(color: Colors.grey),
                            ),
                          ],
                        ),
                      )
                    : ListView.builder(
                        itemCount: gnss.logEntries.length,
                        itemBuilder: (context, index) {
                          final entry = gnss.logEntries[index];
                          return Card(
                            child: ListTile(
                              leading: const Icon(Icons.insert_drive_file),
                              title: Text(entry.fileName),
                              subtitle: Column(
                                crossAxisAlignment: CrossAxisAlignment.start,
                                children: [
                                  Text(
                                    '${_dateFormat.format(entry.timestamp)} • ${_timeFormat.format(entry.timestamp)}',
                                  ),
                                  Text(
                                    'Duration: ${_formatDuration(entry.duration)} • Size: ${_formatFileSize(entry.sizeBytes)}',
                                  ),
                                ],
                              ),
                              trailing: PopupMenuButton(
                                itemBuilder: (context) => [
                                  const PopupMenuItem(
                                    value: 'download',
                                    child: Row(
                                      children: [
                                        Icon(Icons.download),
                                        SizedBox(width: 8),
                                        Text('Download'),
                                      ],
                                    ),
                                  ),
                                  const PopupMenuItem(
                                    value: 'delete',
                                    child: Row(
                                      children: [
                                        Icon(Icons.delete, color: Colors.red),
                                        SizedBox(width: 8),
                                        Text('Delete'),
                                      ],
                                    ),
                                  ),
                                ],
                                onSelected: (value) {
                                  switch (value) {
                                    case 'download':
                                      _downloadLog(entry);
                                      break;
                                    case 'delete':
                                      _deleteLog(entry);
                                      break;
                                  }
                                },
                              ),
                              isThreeLine: true,
                            ),
                          );
                        },
                      ),
                ),
              ],
            ),
          );
        },
      ),
    );
  }
}
