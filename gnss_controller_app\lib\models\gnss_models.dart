// GNSS Data Models

enum ConnectionType { classic, ble }

enum ConnectionStatus { disconnected, connecting, connected, error }

enum GnssMode { ppp, rover, averageBase, knownBase }

enum CorrectionMode { bluetooth, ntrip, lora }

enum LoggingStatus { stopped, recording, paused }

class GnssBluetoothDevice {
  final String name;
  final String address;
  final bool isPaired;
  final ConnectionType type;

  GnssBluetoothDevice({
    required this.name,
    required this.address,
    required this.isPaired,
    required this.type,
  });
}

class GnssConfiguration {
  final GnssMode mode;
  final int frequency; // Hz for rover mode
  final int minimumTime; // seconds for average base
  final double minimumAccuracy; // meters
  final double? latitude; // degrees for known base
  final double? longitude; // degrees for known base
  final double? elevation; // meters for known base

  GnssConfiguration({
    required this.mode,
    this.frequency = 1,
    this.minimumTime = 60,
    this.minimumAccuracy = 1.0,
    this.latitude,
    this.longitude,
    this.elevation,
  });

  GnssConfiguration copyWith({
    GnssMode? mode,
    int? frequency,
    int? minimumTime,
    double? minimumAccuracy,
    double? latitude,
    double? longitude,
    double? elevation,
  }) {
    return GnssConfiguration(
      mode: mode ?? this.mode,
      frequency: frequency ?? this.frequency,
      minimumTime: minimumTime ?? this.minimumTime,
      minimumAccuracy: minimumAccuracy ?? this.minimumAccuracy,
      latitude: latitude ?? this.latitude,
      longitude: longitude ?? this.longitude,
      elevation: elevation ?? this.elevation,
    );
  }
}

class CorrectionConfiguration {
  final bool autoRestart;
  final CorrectionMode mode;
  final LoRaSettings loraSettings;

  CorrectionConfiguration({
    required this.autoRestart,
    required this.mode,
    required this.loraSettings,
  });

  CorrectionConfiguration copyWith({
    bool? autoRestart,
    CorrectionMode? mode,
    LoRaSettings? loraSettings,
  }) {
    return CorrectionConfiguration(
      autoRestart: autoRestart ?? this.autoRestart,
      mode: mode ?? this.mode,
      loraSettings: loraSettings ?? this.loraSettings,
    );
  }
}

class LoRaSettings {
  final int channel; // 1-15
  final double airDataRate; // kbps
  final int transmissionPower; // dBm

  LoRaSettings({
    required this.channel,
    required this.airDataRate,
    required this.transmissionPower,
  });

  LoRaSettings copyWith({
    int? channel,
    double? airDataRate,
    int? transmissionPower,
  }) {
    return LoRaSettings(
      channel: channel ?? this.channel,
      airDataRate: airDataRate ?? this.airDataRate,
      transmissionPower: transmissionPower ?? this.transmissionPower,
    );
  }
}

class GnssSettings {
  final int cutoffAngle; // degrees
  final bool multipathMitigation;
  final bool antiJamming;

  GnssSettings({
    required this.cutoffAngle,
    required this.multipathMitigation,
    required this.antiJamming,
  });

  GnssSettings copyWith({
    int? cutoffAngle,
    bool? multipathMitigation,
    bool? antiJamming,
  }) {
    return GnssSettings(
      cutoffAngle: cutoffAngle ?? this.cutoffAngle,
      multipathMitigation: multipathMitigation ?? this.multipathMitigation,
      antiJamming: antiJamming ?? this.antiJamming,
    );
  }
}

class LogEntry {
  final String fileName;
  final DateTime timestamp;
  final int sizeBytes;
  final Duration duration;

  LogEntry({
    required this.fileName,
    required this.timestamp,
    required this.sizeBytes,
    required this.duration,
  });
}

class TerminalMessage {
  final String message;
  final DateTime timestamp;
  final bool isCommand; // true for sent commands, false for received messages

  TerminalMessage({
    required this.message,
    required this.timestamp,
    required this.isCommand,
  });
}

class CorrectionStatus {
  final bool bluetoothActive;
  final bool ntripActive;
  final bool loraActive;

  CorrectionStatus({
    required this.bluetoothActive,
    required this.ntripActive,
    required this.loraActive,
  });
}
