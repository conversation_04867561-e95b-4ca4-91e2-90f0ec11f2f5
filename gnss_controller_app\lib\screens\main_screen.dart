import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../providers/bluetooth_provider.dart';
import 'connection_screen.dart';
import 'wifi_screen.dart';
import 'gnss_mode_screen.dart';
import 'corrections_screen.dart';
import 'settings_screen.dart';
import 'logging_screen.dart';
import 'terminal_screen.dart';

class MainScreen extends StatefulWidget {
  const MainScreen({super.key});

  @override
  State<MainScreen> createState() => _MainScreenState();
}

class _MainScreenState extends State<MainScreen> {
  void _navigateToScreen(Widget screen, String title) {
    Navigator.of(context).push(
      MaterialPageRoute(
        builder: (context) => Scaffold(
          appBar: AppBar(
            title: Text(title),
            backgroundColor: Theme.of(context).colorScheme.surface,
            foregroundColor: Theme.of(context).colorScheme.onSurface,
          ),
          body: screen,
        ),
      ),
    );
  }

  void _navigateToWiFiScreen() {
    Navigator.of(context).push(
      MaterialPageRoute(
        builder: (context) => Scaffold(
          appBar: AppBar(
            title: const Text('Wi-Fi Networks'),
            backgroundColor: Theme.of(context).colorScheme.surface,
            foregroundColor: Theme.of(context).colorScheme.onSurface,
          ),
          body: const WiFiScreen(),
        ),
      ),
    );
  }

  void _openNTRIPBrowser() async {
    final bluetooth = context.read<BluetoothProvider>();
    final deviceIP = bluetooth.connectedDevice?.address ?? '*************';
    final ntripUrl = 'http://$deviceIP:2101';

    // Show a dialog with web view or open external browser
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('NTRIP Server'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            const Text('Opening NTRIP server at:'),
            const SizedBox(height: 8),
            SelectableText(
              ntripUrl,
              style: const TextStyle(
                fontFamily: 'monospace',
                color: Colors.blue,
              ),
            ),
            const SizedBox(height: 16),
            const Text('This will open the NTRIP web interface in your browser.'),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Cancel'),
          ),
          ElevatedButton(
            onPressed: () {
              Navigator.of(context).pop();
              // In a real app, you would use url_launcher here
              ScaffoldMessenger.of(context).showSnackBar(
                SnackBar(
                  content: Text('Opening $ntripUrl'),
                  action: SnackBarAction(
                    label: 'Copy URL',
                    onPressed: () {
                      // Copy URL to clipboard
                    },
                  ),
                ),
              );
            },
            child: const Text('Open'),
          ),
        ],
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.grey[100],
      appBar: AppBar(
        title: Row(
          children: [
            Container(
              padding: const EdgeInsets.all(6),
              decoration: BoxDecoration(
                color: Colors.blue,
                borderRadius: BorderRadius.circular(4),
              ),
              child: const Text(
                'BG',
                style: TextStyle(
                  color: Colors.white,
                  fontSize: 12,
                  fontWeight: FontWeight.bold,
                ),
              ),
            ),
            const SizedBox(width: 8),
            const Text('Bedo GNSS'),
            const Spacer(),
            Consumer<BluetoothProvider>(
              builder: (context, bluetooth, child) {
                return Container(
                  padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                  decoration: BoxDecoration(
                    color: bluetooth.isConnected ? Colors.green : Colors.red,
                    borderRadius: BorderRadius.circular(12),
                  ),
                  child: Text(
                    bluetooth.isConnected ? 'Connected' : 'No solution',
                    style: const TextStyle(
                      color: Colors.white,
                      fontSize: 12,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                );
              },
            ),
          ],
        ),
        backgroundColor: Colors.black,
        foregroundColor: Colors.white,
        elevation: 0,
      ),
      body: Column(
        children: [
          // Header Section
          Container(
            width: double.infinity,
            padding: const EdgeInsets.symmetric(vertical: 16),
            color: Colors.grey[200],
            child: const Text(
              'Receivers',
              textAlign: TextAlign.center,
              style: TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.w500,
                color: Colors.black87,
              ),
            ),
          ),

          // Connected Device Section
          Container(
            width: double.infinity,
            padding: const EdgeInsets.all(16),
            color: Colors.white,
            child: const Text(
              'Connected',
              style: TextStyle(
                fontSize: 16,
                fontWeight: FontWeight.w500,
                color: Colors.black87,
              ),
            ),
          ),

          // Device Info Card
          Container(
            margin: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              color: Colors.white,
              borderRadius: BorderRadius.circular(8),
              boxShadow: [
                BoxShadow(
                  color: Colors.grey.withOpacity(0.1),
                  spreadRadius: 1,
                  blurRadius: 3,
                  offset: const Offset(0, 1),
                ),
              ],
            ),
            child: Consumer<BluetoothProvider>(
              builder: (context, bluetooth, child) {
                return Row(
                  children: [
                    Container(
                      padding: const EdgeInsets.all(8),
                      decoration: BoxDecoration(
                        color: Colors.blue[100],
                        borderRadius: BorderRadius.circular(4),
                      ),
                      child: const Text(
                        'BG',
                        style: TextStyle(
                          fontSize: 12,
                          fontWeight: FontWeight.bold,
                          color: Colors.blue,
                        ),
                      ),
                    ),
                    const SizedBox(width: 12),
                    Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        GestureDetector(
                          onTap: () => _openNTRIPBrowser(),
                          child: Text(
                            bluetooth.connectedDevice?.name ?? 'NTRIP',
                            style: const TextStyle(
                              fontSize: 16,
                              fontWeight: FontWeight.w500,
                              color: Colors.blue,
                              decoration: TextDecoration.underline,
                            ),
                          ),
                        ),
                        Row(
                          children: [
                            const Icon(Icons.wifi, size: 16, color: Colors.grey),
                            const SizedBox(width: 4),
                            Text(
                              bluetooth.connectedDevice?.address ?? '*************',
                              style: const TextStyle(
                                fontSize: 14,
                                color: Colors.grey,
                              ),
                            ),
                          ],
                        ),
                      ],
                    ),
                  ],
                );
              },
            ),
          ),

          // Menu Items
          Expanded(
            child: Container(
              color: Colors.white,
              child: ListView(
                padding: const EdgeInsets.symmetric(horizontal: 16),
                children: [
                  _buildMenuItem(
                    icon: Icons.bar_chart,
                    iconColor: Colors.orange,
                    title: 'Status',
                    subtitle: '',
                    onTap: () => _navigateToScreen(const ConnectionScreen(), 'Status'),
                  ),
                  _buildMenuItem(
                    icon: Icons.check_circle,
                    iconColor: Colors.blue,
                    title: 'Correction input',
                    subtitle: 'Off',
                    onTap: () => _navigateToScreen(const CorrectionsScreen(), 'Correction input'),
                  ),
                  _buildMenuItem(
                    icon: Icons.radio,
                    iconColor: Colors.lightBlue,
                    title: 'Base output 1',
                    subtitle: 'LoRa radio',
                    onTap: () => _navigateToScreen(const GnssModeScreen(), 'Base output 1'),
                  ),
                  _buildMenuItem(
                    icon: Icons.settings_input_antenna,
                    iconColor: Colors.blue,
                    title: 'Base settings',
                    subtitle: '',
                    onTap: () => _navigateToScreen(const SettingsScreen(), 'Base settings'),
                  ),
                  _buildMenuItem(
                    icon: Icons.fiber_manual_record,
                    iconColor: Colors.red,
                    title: 'Logging',
                    subtitle: 'Recording',
                    trailing: const Icon(Icons.fiber_manual_record, color: Colors.red, size: 12),
                    onTap: () => _navigateToScreen(const LoggingScreen(), 'Logging'),
                  ),
                  _buildMenuItem(
                    icon: Icons.wifi,
                    iconColor: Colors.orange,
                    title: 'Wi-Fi',
                    subtitle: '',
                    onTap: () => _navigateToWiFiScreen(),
                  ),
                  _buildMenuItem(
                    icon: Icons.terminal,
                    iconColor: Colors.lightBlue,
                    title: 'Terminal',
                    subtitle: '',
                    onTap: () => _navigateToScreen(const TerminalScreen(), 'Terminal'),
                  ),
                ],
              ),
            ),
          ),
        ],
      ),
      bottomNavigationBar: BottomNavigationBar(
        type: BottomNavigationBarType.fixed,
        selectedItemColor: Colors.blue,
        unselectedItemColor: Colors.grey,
        currentIndex: 0,
        onTap: (index) {
          if (index == 2) {
            // Navigate to Terminal screen when Terminal tab is tapped
            _navigateToScreen(const TerminalScreen(), 'Terminal');
          }
          // Handle other tabs as needed
        },
        items: const [
          BottomNavigationBarItem(
            icon: Icon(Icons.router),
            label: 'Receivers',
          ),
          BottomNavigationBarItem(
            icon: Icon(Icons.map),
            label: 'Survey',
          ),
          BottomNavigationBarItem(
            icon: Icon(Icons.terminal),
            label: 'Terminal',
          ),
        ],
      ),
    );
  }

  Widget _buildMenuItem({
    required IconData icon,
    required Color iconColor,
    required String title,
    required String subtitle,
    Widget? trailing,
    required VoidCallback onTap,
  }) {
    return Container(
      margin: const EdgeInsets.symmetric(vertical: 4),
      child: ListTile(
        leading: Container(
          padding: const EdgeInsets.all(8),
          decoration: BoxDecoration(
            color: iconColor.withOpacity(0.1),
            borderRadius: BorderRadius.circular(8),
          ),
          child: Icon(
            icon,
            color: iconColor,
            size: 24,
          ),
        ),
        title: Text(
          title,
          style: const TextStyle(
            fontSize: 16,
            fontWeight: FontWeight.w500,
            color: Colors.black87,
          ),
        ),
        subtitle: subtitle.isNotEmpty
            ? Text(
                subtitle,
                style: const TextStyle(
                  fontSize: 14,
                  color: Colors.grey,
                ),
              )
            : null,
        trailing: trailing ?? const Icon(Icons.chevron_right, color: Colors.grey),
        onTap: onTap,
        contentPadding: const EdgeInsets.symmetric(horizontal: 0, vertical: 4),
      ),
    );
  }
}
