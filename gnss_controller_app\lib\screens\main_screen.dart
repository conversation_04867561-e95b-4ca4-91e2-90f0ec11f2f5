import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../providers/bluetooth_provider.dart';
import '../models/gnss_models.dart';
import 'connection_screen.dart';
import 'gnss_mode_screen.dart';
import 'corrections_screen.dart';
import 'settings_screen.dart';
import 'logging_screen.dart';
import 'terminal_screen.dart';

class MainScreen extends StatefulWidget {
  const MainScreen({super.key});

  @override
  State<MainScreen> createState() => _MainScreenState();
}

class _MainScreenState extends State<MainScreen> {
  int _currentIndex = 0;

  final List<Widget> _screens = [
    const ConnectionScreen(),
    const GnssModeScreen(),
    const CorrectionsScreen(),
    const SettingsScreen(),
    const LoggingScreen(),
    const TerminalScreen(),
  ];

  final List<BottomNavigationBarItem> _navItems = [
    const BottomNavigationBarItem(
      icon: Icon(Icons.bluetooth),
      label: 'Connection',
    ),
    const BottomNavigationBarItem(
      icon: Icon(Icons.gps_fixed),
      label: 'GNSS Mode',
    ),
    const BottomNavigationBarItem(
      icon: Icon(Icons.signal_cellular_alt),
      label: 'Corrections',
    ),
    const BottomNavigationBarItem(
      icon: Icon(Icons.settings),
      label: 'Settings',
    ),
    const BottomNavigationBarItem(
      icon: Icon(Icons.save),
      label: 'Logging',
    ),
    const BottomNavigationBarItem(
      icon: Icon(Icons.terminal),
      label: 'Terminal',
    ),
  ];

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Row(
          children: [
            const Icon(Icons.satellite_alt, size: 28),
            const SizedBox(width: 8),
            const Text('GNSS Controller'),
            const Spacer(),
            Consumer<BluetoothProvider>(
              builder: (context, bluetooth, child) {
                return Row(
                  children: [
                    Icon(
                      _getConnectionIcon(bluetooth.connectionStatus),
                      color: _getConnectionColor(bluetooth.connectionStatus),
                      size: 20,
                    ),
                    const SizedBox(width: 4),
                    Text(
                      bluetooth.connectedDevice?.name ?? 'No Device',
                      style: Theme.of(context).textTheme.bodySmall,
                    ),
                  ],
                );
              },
            ),
          ],
        ),
        backgroundColor: Theme.of(context).colorScheme.surface,
        foregroundColor: Theme.of(context).colorScheme.onSurface,
      ),
      body: IndexedStack(
        index: _currentIndex,
        children: _screens,
      ),
      bottomNavigationBar: BottomNavigationBar(
        currentIndex: _currentIndex,
        onTap: (index) {
          setState(() {
            _currentIndex = index;
          });
        },
        type: BottomNavigationBarType.fixed,
        selectedItemColor: Theme.of(context).colorScheme.primary,
        unselectedItemColor: Theme.of(context).colorScheme.onSurface.withOpacity(0.6),
        items: _navItems,
      ),
    );
  }

  IconData _getConnectionIcon(ConnectionStatus status) {
    switch (status) {
      case ConnectionStatus.connected:
        return Icons.bluetooth_connected;
      case ConnectionStatus.connecting:
        return Icons.bluetooth_searching;
      case ConnectionStatus.disconnected:
        return Icons.bluetooth_disabled;
      case ConnectionStatus.error:
        return Icons.bluetooth_disabled;
    }
  }

  Color _getConnectionColor(ConnectionStatus status) {
    switch (status) {
      case ConnectionStatus.connected:
        return Colors.green;
      case ConnectionStatus.connecting:
        return Colors.orange;
      case ConnectionStatus.disconnected:
        return Colors.grey;
      case ConnectionStatus.error:
        return Colors.red;
    }
  }
}
