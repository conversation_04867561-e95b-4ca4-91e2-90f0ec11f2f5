import 'package:flutter/foundation.dart';
import 'package:intl/intl.dart';
import '../models/gnss_models.dart';

class TerminalProvider extends ChangeNotifier {
  final List<TerminalMessage> _messages = [];
  final DateFormat _timeFormat = DateFormat('HH:mm:ss');

  List<TerminalMessage> get messages => List.unmodifiable(_messages);

  void addMessage(String message, {bool isCommand = false}) {
    final terminalMessage = TerminalMessage(
      message: message,
      timestamp: DateTime.now(),
      isCommand: isCommand,
    );
    
    _messages.add(terminalMessage);
    
    // Keep only the last 1000 messages to prevent memory issues
    if (_messages.length > 1000) {
      _messages.removeAt(0);
    }
    
    notifyListeners();
  }

  void addCommandEcho(String command) {
    final timestamp = _timeFormat.format(DateTime.now());
    addMessage('[$timestamp] >> Sent: $command', isCommand: true);
  }

  void addResponse(String response) {
    final timestamp = _timeFormat.format(DateTime.now());
    addMessage('[$timestamp] << Received: $response', isCommand: false);
  }

  void addSystemMessage(String message) {
    final timestamp = _timeFormat.format(DateTime.now());
    addMessage('[$timestamp] System: $message', isCommand: false);
  }

  void clearMessages() {
    _messages.clear();
    notifyListeners();
  }

  String getFormattedMessages() {
    return _messages.map((msg) {
      final timestamp = _timeFormat.format(msg.timestamp);
      final prefix = msg.isCommand ? '>> ' : '<< ';
      return '[$timestamp] $prefix${msg.message}';
    }).join('\n');
  }
}
