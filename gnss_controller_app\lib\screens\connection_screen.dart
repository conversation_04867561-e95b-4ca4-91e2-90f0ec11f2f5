import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../providers/bluetooth_provider.dart';
import '../providers/terminal_provider.dart';
import '../models/gnss_models.dart';

class ConnectionScreen extends StatefulWidget {
  const ConnectionScreen({super.key});

  @override
  State<ConnectionScreen> createState() => _ConnectionScreenState();
}

class _ConnectionScreenState extends State<ConnectionScreen> {
  bool _isScanning = false;

  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _checkBluetoothStatus();
    });
  }

  Future<void> _checkBluetoothStatus() async {
    final bluetooth = context.read<BluetoothProvider>();
    if (!bluetooth.isBluetoothEnabled) {
      await bluetooth.enableBluetooth();
    }
  }

  Future<void> _scanForDevices() async {
    setState(() {
      _isScanning = true;
    });

    final bluetooth = context.read<BluetoothProvider>();
    final terminal = context.read<TerminalProvider>();

    terminal.addSystemMessage('Scanning for Bluetooth devices...');
    await bluetooth.scanForDevices();
    terminal.addSystemMessage('Scan completed');

    setState(() {
      _isScanning = false;
    });
  }

  Future<void> _connectToDevice(GnssBluetoothDevice device) async {
    final bluetooth = context.read<BluetoothProvider>();
    final terminal = context.read<TerminalProvider>();

    terminal.addSystemMessage('Connecting to ${device.name}...');
    final success = await bluetooth.connectToDevice(device);

    if (success) {
      terminal.addSystemMessage('Connected to ${device.name}');
    } else {
      terminal.addSystemMessage('Failed to connect to ${device.name}');
    }
  }

  Future<void> _disconnect() async {
    final bluetooth = context.read<BluetoothProvider>();
    final terminal = context.read<TerminalProvider>();

    terminal.addSystemMessage('Disconnecting...');
    await bluetooth.disconnect();
    terminal.addSystemMessage('Disconnected');
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: Consumer<BluetoothProvider>(
        builder: (context, bluetooth, child) {
          return Padding(
            padding: const EdgeInsets.all(16.0),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // Bluetooth Status Card
                Card(
                  child: Padding(
                    padding: const EdgeInsets.all(16.0),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Row(
                          children: [
                            Icon(
                              bluetooth.isBluetoothEnabled
                                ? Icons.bluetooth
                                : Icons.bluetooth_disabled,
                              color: bluetooth.isBluetoothEnabled
                                ? Colors.blue
                                : Colors.grey,
                            ),
                            const SizedBox(width: 8),
                            Text(
                              'Bluetooth Status',
                              style: Theme.of(context).textTheme.titleMedium,
                            ),
                          ],
                        ),
                        const SizedBox(height: 8),
                        Text(
                          bluetooth.isBluetoothEnabled
                            ? 'Enabled'
                            : 'Disabled',
                          style: TextStyle(
                            color: bluetooth.isBluetoothEnabled
                              ? Colors.green
                              : Colors.red,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                        if (!bluetooth.isBluetoothEnabled) ...[
                          const SizedBox(height: 8),
                          ElevatedButton(
                            onPressed: () async {
                              await bluetooth.enableBluetooth();
                            },
                            child: const Text('Enable Bluetooth'),
                          ),
                        ],
                      ],
                    ),
                  ),
                ),

                const SizedBox(height: 16),

                // Connection Status Card
                if (bluetooth.isBluetoothEnabled) ...[
                  Card(
                    child: Padding(
                      padding: const EdgeInsets.all(16.0),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Row(
                            children: [
                              Icon(
                                _getConnectionIcon(bluetooth.connectionStatus),
                                color: _getConnectionColor(bluetooth.connectionStatus),
                              ),
                              const SizedBox(width: 8),
                              Text(
                                'Connection Status',
                                style: Theme.of(context).textTheme.titleMedium,
                              ),
                            ],
                          ),
                          const SizedBox(height: 8),
                          Text(
                            _getConnectionStatusText(bluetooth.connectionStatus),
                            style: TextStyle(
                              color: _getConnectionColor(bluetooth.connectionStatus),
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                          if (bluetooth.connectedDevice != null) ...[
                            const SizedBox(height: 8),
                            Text('Device: ${bluetooth.connectedDevice!.name}'),
                            Text('Address: ${bluetooth.connectedDevice!.address}'),
                          ],
                        ],
                      ),
                    ),
                  ),

                  const SizedBox(height: 16),

                  // Action Buttons
                  Row(
                    children: [
                      Expanded(
                        child: ElevatedButton.icon(
                          onPressed: bluetooth.isConnected ? _disconnect : null,
                          icon: const Icon(Icons.bluetooth_disabled),
                          label: const Text('Disconnect'),
                          style: ElevatedButton.styleFrom(
                            backgroundColor: Colors.red,
                            foregroundColor: Colors.white,
                          ),
                        ),
                      ),
                      const SizedBox(width: 8),
                      Expanded(
                        child: ElevatedButton.icon(
                          onPressed: _isScanning ? null : _scanForDevices,
                          icon: _isScanning
                            ? const SizedBox(
                                width: 16,
                                height: 16,
                                child: CircularProgressIndicator(strokeWidth: 2),
                              )
                            : const Icon(Icons.search),
                          label: Text(_isScanning ? 'Scanning...' : 'Scan'),
                        ),
                      ),
                    ],
                  ),

                  const SizedBox(height: 16),

                  // Device List
                  Text(
                    'Available Devices',
                    style: Theme.of(context).textTheme.titleMedium,
                  ),
                  const SizedBox(height: 8),
                  Expanded(
                    child: bluetooth.availableDevices.isEmpty
                      ? const Center(
                          child: Text('No devices found. Tap "Scan" to search for devices.'),
                        )
                      : ListView.builder(
                          itemCount: bluetooth.availableDevices.length,
                          itemBuilder: (context, index) {
                            final device = bluetooth.availableDevices[index];
                            final isConnected = bluetooth.connectedDevice?.address == device.address;

                            return Card(
                              child: ListTile(
                                leading: Icon(
                                  device.isPaired
                                    ? Icons.bluetooth_connected
                                    : Icons.bluetooth,
                                  color: isConnected ? Colors.green : Colors.blue,
                                ),
                                title: Text(device.name),
                                subtitle: Text(device.address),
                                trailing: isConnected
                                  ? const Icon(Icons.check_circle, color: Colors.green)
                                  : bluetooth.connectionStatus == ConnectionStatus.connecting
                                    ? const SizedBox(
                                        width: 20,
                                        height: 20,
                                        child: CircularProgressIndicator(strokeWidth: 2),
                                      )
                                    : IconButton(
                                        icon: const Icon(Icons.connect_without_contact),
                                        onPressed: () => _connectToDevice(device),
                                      ),
                                onTap: isConnected ? null : () => _connectToDevice(device),
                              ),
                            );
                          },
                        ),
                  ),
                ],
              ],
            ),
          );
        },
      ),
    );
  }

  IconData _getConnectionIcon(ConnectionStatus status) {
    switch (status) {
      case ConnectionStatus.connected:
        return Icons.bluetooth_connected;
      case ConnectionStatus.connecting:
        return Icons.bluetooth_searching;
      case ConnectionStatus.disconnected:
        return Icons.bluetooth_disabled;
      case ConnectionStatus.error:
        return Icons.error;
    }
  }

  Color _getConnectionColor(ConnectionStatus status) {
    switch (status) {
      case ConnectionStatus.connected:
        return Colors.green;
      case ConnectionStatus.connecting:
        return Colors.orange;
      case ConnectionStatus.disconnected:
        return Colors.grey;
      case ConnectionStatus.error:
        return Colors.red;
    }
  }

  String _getConnectionStatusText(ConnectionStatus status) {
    switch (status) {
      case ConnectionStatus.connected:
        return 'Connected';
      case ConnectionStatus.connecting:
        return 'Connecting...';
      case ConnectionStatus.disconnected:
        return 'Disconnected';
      case ConnectionStatus.error:
        return 'Connection Error';
    }
  }
}
